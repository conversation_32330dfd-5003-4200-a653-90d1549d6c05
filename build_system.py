#!/usr/bin/env python3
"""
Build System for Neural Networks Mathematics Guide
Provides automated page counting, formatting, and document management
"""

import re
import os
from datetime import datetime
from typing import Dict, List, Tuple
import argparse

class DocumentBuilder:
    def __init__(self, source_file: str = "neural_networks_mathematics_guide.md"):
        self.source_file = source_file
        self.words_per_page = 250  # Standard estimation for academic documents
        self.current_stats = {}
        
    def count_words_and_estimate_pages(self) -> Dict[str, int]:
        """Count words in document and estimate page count"""
        if not os.path.exists(self.source_file):
            return {"words": 0, "pages": 0, "chapters": 0}
            
        with open(self.source_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove markdown formatting for word count
        text_only = re.sub(r'[#*`\[\]()_-]', '', content)
        text_only = re.sub(r'http[s]?://\S+', '', text_only)  # Remove URLs
        
        words = len(text_only.split())
        estimated_pages = max(1, round(words / self.words_per_page))
        
        # Count chapters
        chapters = len(re.findall(r'^#### Chapter \d+:', content, re.MULTILINE))
        
        return {
            "words": words,
            "pages": estimated_pages,
            "chapters": chapters
        }
    
    def analyze_chapter_distribution(self) -> Dict[str, Dict[str, int]]:
        """Analyze word/page distribution by chapter"""
        if not os.path.exists(self.source_file):
            return {}
            
        with open(self.source_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Split content by chapters
        chapter_pattern = r'^#### Chapter (\d+): (.+?)$'
        chapters = re.split(chapter_pattern, content, flags=re.MULTILINE)
        
        chapter_stats = {}
        for i in range(1, len(chapters), 3):  # Every 3rd element is chapter content
            if i + 2 < len(chapters):
                chapter_num = chapters[i]
                chapter_title = chapters[i + 1]
                chapter_content = chapters[i + 2]
                
                # Count words in chapter content
                text_only = re.sub(r'[#*`\[\]()_-]', '', chapter_content)
                words = len(text_only.split())
                pages = max(1, round(words / self.words_per_page))
                
                chapter_stats[f"Chapter {chapter_num}"] = {
                    "title": chapter_title.strip(),
                    "words": words,
                    "pages": pages
                }
        
        return chapter_stats
    
    def update_metadata(self) -> None:
        """Update document metadata with current statistics"""
        if not os.path.exists(self.source_file):
            return
            
        with open(self.source_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        stats = self.count_words_and_estimate_pages()
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        # Update the metadata section
        metadata_pattern = r'(---\n\*\*Document Metadata\*\*.*?- Last Updated: )[\d-]+(\n.*?---)'
        replacement = f'\\g<1>{current_date}\\g<2>'
        
        updated_content = re.sub(metadata_pattern, replacement, content, flags=re.DOTALL)
        
        # Add statistics comment at the end of metadata
        stats_comment = f"\n<!-- Current Stats: {stats['words']} words, ~{stats['pages']} pages, {stats['chapters']} chapters -->"
        updated_content = re.sub(r'(---\n\*\*Document Metadata\*\*.*?---)', 
                                f'\\1{stats_comment}', updated_content, flags=re.DOTALL)
        
        with open(self.source_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
    
    def generate_progress_report(self) -> str:
        """Generate a progress report for the document"""
        stats = self.count_words_and_estimate_pages()
        chapter_stats = self.analyze_chapter_distribution()
        
        report = f"""
# Document Progress Report
Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## Overall Statistics
- **Total Words**: {stats['words']:,}
- **Estimated Pages**: {stats['pages']} / 300 target ({stats['pages']/300*100:.1f}%)
- **Chapters**: {stats['chapters']} / 12 planned

## Chapter Breakdown
"""
        
        for chapter, data in chapter_stats.items():
            report += f"- **{chapter}**: {data['title']}\n"
            report += f"  - Words: {data['words']:,}\n"
            report += f"  - Pages: ~{data['pages']}\n\n"
        
        target_remaining = 300 - stats['pages']
        words_needed = target_remaining * self.words_per_page
        
        report += f"""
## Progress Analysis
- **Pages Remaining**: {target_remaining}
- **Words Needed**: ~{words_needed:,}
- **Average per Remaining Chapter**: ~{words_needed // max(1, 12 - stats['chapters']):,} words

## Recommendations
"""
        
        if stats['pages'] < 100:
            report += "- Focus on expanding mathematical foundations (Tier 1)\n"
        elif stats['pages'] < 200:
            report += "- Continue developing neural network fundamentals (Tier 2)\n"
        else:
            report += "- Complete advanced mathematics and applications (Tier 3)\n"
            
        return report
    
    def validate_cross_references(self) -> List[str]:
        """Validate internal cross-references in the document"""
        if not os.path.exists(self.source_file):
            return ["Source file not found"]
            
        with open(self.source_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find all internal links
        internal_links = re.findall(r'\[([^\]]+)\]\(#([^)]+)\)', content)
        
        # Find all anchor targets (headers)
        headers = re.findall(r'^#+\s+(.+)$', content, re.MULTILINE)
        
        # Convert headers to anchor format
        anchors = set()
        for header in headers:
            # Convert to anchor format (lowercase, replace spaces with hyphens, remove special chars)
            anchor = re.sub(r'[^\w\s-]', '', header.lower())
            anchor = re.sub(r'[-\s]+', '-', anchor).strip('-')
            anchors.add(anchor)
        
        # Check for broken links
        broken_links = []
        for link_text, anchor in internal_links:
            if anchor not in anchors:
                broken_links.append(f"Broken link: [{link_text}](#{anchor})")
        
        return broken_links

def main():
    parser = argparse.ArgumentParser(description='Build system for Neural Networks Mathematics Guide')
    parser.add_argument('--count', action='store_true', help='Count words and estimate pages')
    parser.add_argument('--update', action='store_true', help='Update document metadata')
    parser.add_argument('--report', action='store_true', help='Generate progress report')
    parser.add_argument('--validate', action='store_true', help='Validate cross-references')
    parser.add_argument('--all', action='store_true', help='Run all operations')
    
    args = parser.parse_args()
    
    builder = DocumentBuilder()
    
    if args.all or args.count:
        stats = builder.count_words_and_estimate_pages()
        print(f"Words: {stats['words']:,}")
        print(f"Estimated Pages: {stats['pages']} / 300")
        print(f"Chapters: {stats['chapters']} / 12")
    
    if args.all or args.update:
        builder.update_metadata()
        print("Metadata updated")
    
    if args.all or args.report:
        report = builder.generate_progress_report()
        with open('progress_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        print("Progress report generated: progress_report.md")
    
    if args.all or args.validate:
        broken_links = builder.validate_cross_references()
        if broken_links:
            print("Broken cross-references found:")
            for link in broken_links:
                print(f"  - {link}")
        else:
            print("All cross-references are valid")

if __name__ == "__main__":
    main()