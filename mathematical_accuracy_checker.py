#!/usr/bin/env python3
"""
Mathematical Accuracy Verification Tool
For Neural Networks Mathematics Guide

This tool systematically verifies:
1. All equations and mathematical derivations
2. Consistency of mathematical notation
3. All worked examples and computational results
"""

import re
import math
import numpy as np
from typing import List, Dict, Tuple, Any
import json

class MathematicalAccuracyChecker:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.notation_registry = {}
        self.equation_registry = {}
        self.example_results = {}
        
    def check_document(self, document_path: str) -> Dict[str, Any]:
        """Main method to check mathematical accuracy of the document"""
        print("🔍 Starting Mathematical Accuracy Verification...")
        
        try:
            with open(document_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"📄 Document loaded: {len(content)} characters")
            
            # 1. Extract and verify equations
            try:
                print("1️⃣ Extracting equations...")
                equations = self.extract_equations(content)
                print(f"   Found {len(equations)} equations")
                self.verify_equations(equations)
            except Exception as e:
                self.errors.append(f"Error in equation verification: {str(e)}")
            
            # 2. Check mathematical notation consistency
            try:
                print("2️⃣ Checking notation consistency...")
                self.check_notation_consistency(content)
            except Exception as e:
                self.warnings.append(f"Error in notation consistency check: {str(e)}")
            
            # 3. Verify worked examples
            try:
                print("3️⃣ Verifying worked examples...")
                examples = self.extract_examples(content)
                print(f"   Found {len(examples)} examples")
                self.verify_examples(examples)
            except Exception as e:
                self.warnings.append(f"Error in example verification: {str(e)}")
            
            # 4. Check cross-references
            try:
                print("4️⃣ Checking cross-references...")
                self.verify_cross_references(content)
            except Exception as e:
                self.warnings.append(f"Error in cross-reference verification: {str(e)}")
            
            # 5. Validate mathematical constants
            try:
                print("5️⃣ Validating mathematical constants...")
                self.verify_mathematical_constants(content)
            except Exception as e:
                self.warnings.append(f"Error in constant verification: {str(e)}")
            
            print("✅ Verification complete!")
            return self.generate_report()
            
        except Exception as e:
            self.errors.append(f"Critical error reading document: {str(e)}")
            return self.generate_report()
    
    def extract_equations(self, content: str) -> List[Dict]:
        """Extract all mathematical equations from the document"""
        equations = []
        
        try:
            # Pattern for LaTeX equations (both inline and display)
            latex_patterns = [
                r'\$([^$]+)\$',  # Inline equations
                r'\$\$([^$]+)\$\$',  # Display equations
                r'\\begin\{equation\}(.*?)\\end\{equation\}',  # Equation environments
                r'\\begin\{align\}(.*?)\\end\{align\}'  # Align environments
            ]
            
            for pattern in latex_patterns:
                try:
                    latex_matches = re.finditer(pattern, content, re.DOTALL)
                    for match in latex_matches:
                        equations.append({
                            'type': 'latex',
                            'content': match.group(1).strip(),
                            'position': match.start(),
                            'context': self.get_context(content, match.start())
                        })
                except Exception as e:
                    self.warnings.append(f"Error processing LaTeX pattern {pattern}: {str(e)}")
            
            # Pattern for code block equations
            try:
                code_pattern = r'```(?:python|math)?\n([^`]+)\n```'
                code_matches = re.finditer(code_pattern, content, re.DOTALL)
                
                for match in code_matches:
                    code_content = match.group(1)
                    if any(op in code_content for op in ['=', '+', '-', '*', '/', '^']):
                        equations.append({
                            'type': 'code',
                            'content': code_content.strip(),
                            'position': match.start(),
                            'context': self.get_context(content, match.start())
                        })
            except Exception as e:
                self.warnings.append(f"Error processing code blocks: {str(e)}")
                
        except Exception as e:
            self.errors.append(f"Critical error in extract_equations: {str(e)}")
        
        return equations
    
    def verify_equations(self, equations: List[Dict]) -> None:
        """Verify mathematical correctness of equations"""
        print("📐 Verifying equations...")
        
        for eq in equations:
            try:
                if eq['type'] == 'latex':
                    self.verify_latex_equation(eq)
                elif eq['type'] == 'code':
                    self.verify_code_equation(eq)
            except Exception as e:
                self.errors.append(f"Error verifying equation at position {eq['position']}: {str(e)}")
    
    def verify_latex_equation(self, equation: Dict) -> None:
        """Verify LaTeX mathematical equations"""
        content = equation['content']
        
        # Check for common mathematical errors
        if 'frac{' in content:
            # Verify fraction notation
            frac_pattern = r'\\frac\{([^}]+)\}\{([^}]+)\}'
            fractions = re.findall(frac_pattern, content)
            for num, den in fractions:
                if den.strip() == '0':
                    self.errors.append(f"Division by zero in equation: {content}")
        
        # Check for undefined variables
        variables = re.findall(r'[a-zA-Z](?:_[a-zA-Z0-9]+)?', content)
        for var in variables:
            if var not in ['ln', 'log', 'sin', 'cos', 'tan', 'exp', 'sqrt']:
                if var not in self.notation_registry:
                    self.warnings.append(f"Variable '{var}' used without definition in: {content}")
    
    def verify_code_equation(self, equation: Dict) -> None:
        """Verify equations in code blocks"""
        try:
            content = equation['content'].strip()
            lines = content.split('\n')
            
            for line in lines:
                line = line.strip()
                if '=' in line and not line.startswith('#') and line:
                    try:
                        # Simple validation for basic arithmetic
                        if re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*\s*=\s*[0-9+\-*/().\s]+$', line):
                            # Extract the calculation part
                            parts = line.split('=', 1)
                            if len(parts) == 2:
                                calc_part = parts[1].strip()
                                # Basic safety check before eval - only allow safe characters
                                safe_chars = set('0123456789+-*/.() ')
                                if calc_part and all(c in safe_chars for c in calc_part):
                                    try:
                                        # Use a restricted eval for safety
                                        result = eval(calc_part, {"__builtins__": {}}, {})
                                        # Store result for potential cross-checking
                                        var_name = parts[0].strip()
                                        self.example_results[var_name] = result
                                    except (SyntaxError, ValueError, ZeroDivisionError) as e:
                                        self.errors.append(f"Invalid calculation in '{line}': {str(e)}")
                                    except Exception as e:
                                        self.warnings.append(f"Could not verify calculation '{line}': {str(e)}")
                    except Exception as e:
                        self.warnings.append(f"Error processing line '{line}': {str(e)}")
        except Exception as e:
            self.warnings.append(f"Error in verify_code_equation: {str(e)}")
    
    def extract_examples(self, content: str) -> List[Dict]:
        """Extract worked examples from the document"""
        examples = []
        
        # Pattern for examples
        example_pattern = r'\*\*Example \d+\.\d+:([^*]+)\*\*'
        matches = re.finditer(example_pattern, content, re.DOTALL)
        
        for match in matches:
            example_text = match.group(1)
            examples.append({
                'title': match.group(0),
                'content': example_text,
                'position': match.start()
            })
        
        return examples
    
    def verify_examples(self, examples: List[Dict]) -> None:
        """Verify computational results in worked examples"""
        print("🧮 Verifying worked examples...")
        
        for example in examples:
            self.verify_example_calculations(example)
    
    def verify_example_calculations(self, example: Dict) -> None:
        """Verify specific calculations in an example"""
        content = example['content']
        
        # Look for calculation patterns
        calc_patterns = [
            r'(\d+(?:\.\d+)?)\s*[×*]\s*(\d+(?:\.\d+)?)\s*=\s*(\d+(?:\.\d+)?)',
            r'(\d+(?:\.\d+)?)\s*[+]\s*(\d+(?:\.\d+)?)\s*=\s*(\d+(?:\.\d+)?)',
            r'(\d+(?:\.\d+)?)\s*[-]\s*(\d+(?:\.\d+)?)\s*=\s*(\d+(?:\.\d+)?)',
            r'(\d+(?:\.\d+)?)\s*[/÷]\s*(\d+(?:\.\d+)?)\s*=\s*(\d+(?:\.\d+)?)'
        ]
        
        for pattern in calc_patterns:
            matches = re.finditer(pattern, content)
            for match in matches:
                try:
                    a, b, result = float(match.group(1)), float(match.group(2)), float(match.group(3))
                    
                    if '×' in match.group(0) or '*' in match.group(0):
                        expected = a * b
                    elif '+' in match.group(0):
                        expected = a + b
                    elif '-' in match.group(0):
                        expected = a - b
                    elif '/' in match.group(0) or '÷' in match.group(0):
                        expected = a / b if b != 0 else float('inf')
                    
                    if abs(expected - result) > 0.01:  # Allow small rounding errors
                        self.errors.append(f"Calculation error: {match.group(0)} (expected {expected:.2f})")
                        
                except Exception as e:
                    self.warnings.append(f"Could not verify calculation: {match.group(0)}")
    
    def check_notation_consistency(self, content: str) -> None:
        """Check consistency of mathematical notation throughout document"""
        print("📝 Checking notation consistency...")
        
        # Common notation patterns
        notation_patterns = {
            'vectors': r'[a-zA-Z]⃗',
            'matrices': r'[A-Z]',
            'functions': r'[a-zA-Z]\([^)]+\)',
            'subscripts': r'[a-zA-Z]_\{?[a-zA-Z0-9]+\}?',
            'superscripts': r'[a-zA-Z]\^\{?[a-zA-Z0-9]+\}?'
        }
        
        for notation_type, pattern in notation_patterns.items():
            matches = re.findall(pattern, content)
            # Check for consistency in usage
            unique_notations = set(matches)
            if len(unique_notations) > 50:  # Arbitrary threshold
                self.warnings.append(f"Many different {notation_type} notations used - check consistency")
    
    def verify_cross_references(self, content: str) -> None:
        """Verify that cross-references are accurate"""
        print("🔗 Verifying cross-references...")
        
        try:
            # Find all section references
            ref_pattern = r'\[([^\]]+)\]\(#([^)]+)\)'
            references = re.findall(ref_pattern, content)
            
            # Find all section headers
            header_pattern = r'^#+\s+(.+)$'
            headers = re.findall(header_pattern, content, re.MULTILINE)
            
            # Convert headers to anchor format
            anchors = set()
            for header in headers:
                try:
                    # Clean header text to create anchor
                    anchor = header.lower()
                    # Remove common punctuation and replace spaces with hyphens
                    anchor = re.sub(r'[^\w\s-]', '', anchor)
                    anchor = re.sub(r'\s+', '-', anchor)
                    anchor = anchor.strip('-')
                    anchors.add(anchor)
                except Exception as e:
                    self.warnings.append(f"Error processing header '{header}': {str(e)}")
            
            # Check if references exist
            for ref_text, ref_anchor in references:
                if ref_anchor not in anchors:
                    self.warnings.append(f"Broken cross-reference: [{ref_text}](#{ref_anchor})")
                    
        except Exception as e:
            self.warnings.append(f"Error in verify_cross_references: {str(e)}")
    
    def verify_mathematical_constants(self, content: str) -> None:
        """Verify that mathematical constants are used correctly"""
        print("🔢 Verifying mathematical constants...")
        
        constants = {
            'π': 3.14159,
            'pi': 3.14159,
            'e': 2.71828,
            'ln(2)': 0.693,
            '0.693': math.log(2)
        }
        
        for const_name, expected_value in constants.items():
            pattern = f'{re.escape(const_name)}\\s*=\\s*(\\d+\\.\\d+)'
            matches = re.findall(pattern, content)
            
            for match in matches:
                actual_value = float(match)
                if abs(actual_value - expected_value) > 0.01:
                    self.errors.append(f"Incorrect value for {const_name}: {actual_value} (expected ~{expected_value})")
    
    def get_context(self, content: str, position: int, context_length: int = 100) -> str:
        """Get context around a position in the document"""
        start = max(0, position - context_length)
        end = min(len(content), position + context_length)
        return content[start:end]
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive accuracy report"""
        report = {
            'total_errors': len(self.errors),
            'total_warnings': len(self.warnings),
            'errors': self.errors,
            'warnings': self.warnings,
            'status': 'PASS' if len(self.errors) == 0 else 'FAIL',
            'recommendations': []
        }
        
        if self.errors:
            report['recommendations'].append("Fix all mathematical errors before proceeding")
        if self.warnings:
            report['recommendations'].append("Review warnings for potential improvements")
        if not self.errors and not self.warnings:
            report['recommendations'].append("Mathematical accuracy verification passed")
        
        return report

def main():
    """Main execution function"""
    try:
        checker = MathematicalAccuracyChecker()
        
        # Check if the main document exists
        document_path = 'neural_networks_mathematics_guide.md'
        try:
            with open(document_path, 'r', encoding='utf-8') as f:
                # Just check if file can be opened
                pass
        except FileNotFoundError:
            print(f"❌ Error: Document '{document_path}' not found.")
            print("Available files in current directory:")
            import os
            for file in os.listdir('.'):
                if file.endswith('.md'):
                    print(f"  - {file}")
            return False
        except Exception as e:
            print(f"❌ Error reading document: {str(e)}")
            return False
        
        # Check the main document
        print(f"🔍 Checking document: {document_path}")
        report = checker.check_document(document_path)
        
        # Print summary
        print("\n" + "="*60)
        print("📊 MATHEMATICAL ACCURACY VERIFICATION REPORT")
        print("="*60)
        print(f"Status: {report['status']}")
        print(f"Errors: {report['total_errors']}")
        print(f"Warnings: {report['total_warnings']}")
        
        if report['errors']:
            print("\n🚨 ERRORS FOUND:")
            for i, error in enumerate(report['errors'], 1):
                print(f"{i}. {error}")
        
        if report['warnings']:
            print("\n⚠️  WARNINGS:")
            for i, warning in enumerate(report['warnings'], 1):
                print(f"{i}. {warning}")
        
        print("\n📋 RECOMMENDATIONS:")
        for rec in report['recommendations']:
            print(f"• {rec}")
        
        # Save detailed report
        try:
            with open('mathematical_accuracy_report.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"\n📄 Detailed report saved to: mathematical_accuracy_report.json")
        except Exception as e:
            print(f"\n⚠️  Could not save report file: {str(e)}")
        
        return report['status'] == 'PASS'
        
    except Exception as e:
        print(f"❌ Critical error in main(): {str(e)}")
        import traceback
        print("Full traceback:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Fatal error: {str(e)}")
        exit(1)