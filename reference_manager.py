#!/usr/bin/env python3
"""
Cross-Reference and Citation Management System
For Neural Networks Mathematics Guide
"""

import re
import json
import os
from typing import Dict, List, Set, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class Reference:
    """Data class for managing references"""
    id: str
    type: str  # 'equation', 'figure', 'table', 'section', 'requirement'
    chapter: int
    number: int
    title: str
    content: str = ""
    page_estimate: int = 0

@dataclass
class Citation:
    """Data class for managing citations"""
    id: str
    authors: str
    title: str
    source: str
    year: int
    url: str = ""
    doi: str = ""

class ReferenceManager:
    def __init__(self, source_file: str = "neural_networks_mathematics_guide.md"):
        self.source_file = source_file
        self.references: Dict[str, Reference] = {}
        self.citations: Dict[str, Citation] = {}
        self.cross_refs: Dict[str, List[str]] = {}  # Maps reference to locations where it's cited
        
        # Load existing references if available
        self.load_references()
        self.load_citations()
    
    def load_references(self) -> None:
        """Load references from JSON file if it exists"""
        if os.path.exists('references.json'):
            with open('references.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.references = {k: Reference(**v) for k, v in data.items()}
    
    def save_references(self) -> None:
        """Save references to JSON file"""
        data = {k: asdict(v) for k, v in self.references.items()}
        with open('references.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def load_citations(self) -> None:
        """Load citations from JSON file if it exists"""
        if os.path.exists('citations.json'):
            with open('citations.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.citations = {k: Citation(**v) for k, v in data.items()}
    
    def save_citations(self) -> None:
        """Save citations to JSON file"""
        data = {k: asdict(v) for k, v in self.citations.items()}
        with open('citations.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    
    def add_reference(self, ref_type: str, chapter: int, title: str, content: str = "") -> str:
        """Add a new reference and return its ID"""
        # Generate reference ID
        chapter_refs = [r for r in self.references.values() if r.chapter == chapter and r.type == ref_type]
        number = len(chapter_refs) + 1
        ref_id = f"{ref_type}_{chapter}_{number}"
        
        reference = Reference(
            id=ref_id,
            type=ref_type,
            chapter=chapter,
            number=number,
            title=title,
            content=content
        )
        
        self.references[ref_id] = reference
        self.save_references()
        return ref_id
    
    def add_citation(self, authors: str, title: str, source: str, year: int, 
                    url: str = "", doi: str = "") -> str:
        """Add a new citation and return its ID"""
        # Generate citation ID from first author's last name and year
        first_author = authors.split(',')[0].split()[-1] if ',' in authors else authors.split()[-1]
        cite_id = f"{first_author.lower()}{year}"
        
        # Handle duplicates
        counter = 1
        original_id = cite_id
        while cite_id in self.citations:
            cite_id = f"{original_id}_{counter}"
            counter += 1
        
        citation = Citation(
            id=cite_id,
            authors=authors,
            title=title,
            source=source,
            year=year,
            url=url,
            doi=doi
        )
        
        self.citations[cite_id] = citation
        self.save_citations()
        return cite_id
    
    def scan_document_references(self) -> None:
        """Scan the document for existing references and cross-references"""
        if not os.path.exists(self.source_file):
            return
        
        with open(self.source_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find equations
        equation_pattern = r'^\s*\$\$([^$]+)\$\$\s*\((\d+)\.(\d+)\)\s*$'
        equations = re.findall(equation_pattern, content, re.MULTILINE)
        
        for eq_content, chapter, number in equations:
            ref_id = f"equation_{chapter}_{number}"
            if ref_id not in self.references:
                self.add_reference('equation', int(chapter), f"Equation {chapter}.{number}", eq_content.strip())
        
        # Find figures
        figure_pattern = r'!\[([^\]]*)\]\([^)]+\).*?Figure (\d+)\.(\d+)'
        figures = re.findall(figure_pattern, content, re.DOTALL)
        
        for alt_text, chapter, number in figures:
            ref_id = f"figure_{chapter}_{number}"
            if ref_id not in self.references:
                self.add_reference('figure', int(chapter), f"Figure {chapter}.{number}", alt_text)
        
        # Find cross-references
        cross_ref_pattern = r'\[([^\]]+)\]\(#([^)]+)\)'
        cross_refs = re.findall(cross_ref_pattern, content)
        
        for link_text, anchor in cross_refs:
            if anchor not in self.cross_refs:
                self.cross_refs[anchor] = []
            self.cross_refs[anchor].append(link_text)
    
    def generate_reference_list(self, chapter: int = None) -> str:
        """Generate formatted reference list for a chapter or entire document"""
        if chapter:
            refs = [r for r in self.references.values() if r.chapter == chapter]
        else:
            refs = list(self.references.values())
        
        # Group by type
        by_type = {}
        for ref in refs:
            if ref.type not in by_type:
                by_type[ref.type] = []
            by_type[ref.type].append(ref)
        
        # Sort within each type
        for ref_type in by_type:
            by_type[ref_type].sort(key=lambda x: (x.chapter, x.number))
        
        # Generate formatted output
        output = []
        
        if 'equation' in by_type:
            output.append("### Equations")
            for ref in by_type['equation']:
                output.append(f"- **Equation {ref.chapter}.{ref.number}**: {ref.title}")
        
        if 'figure' in by_type:
            output.append("### Figures")
            for ref in by_type['figure']:
                output.append(f"- **Figure {ref.chapter}.{ref.number}**: {ref.title}")
        
        if 'table' in by_type:
            output.append("### Tables")
            for ref in by_type['table']:
                output.append(f"- **Table {ref.chapter}.{ref.number}**: {ref.title}")
        
        return "\n".join(output)
    
    def generate_citation_list(self) -> str:
        """Generate formatted bibliography"""
        citations = sorted(self.citations.values(), key=lambda x: (x.authors.split()[-1], x.year))
        
        output = ["## Bibliography\n"]
        
        for cite in citations:
            citation_text = f"**{cite.id}**: {cite.authors} ({cite.year}). *{cite.title}*. {cite.source}."
            
            if cite.doi:
                citation_text += f" DOI: {cite.doi}"
            elif cite.url:
                citation_text += f" Available at: {cite.url}"
            
            output.append(citation_text)
        
        return "\n".join(output)
    
    def validate_references(self) -> List[str]:
        """Validate all references in the document"""
        issues = []
        
        if not os.path.exists(self.source_file):
            return ["Source file not found"]
        
        with open(self.source_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for undefined references
        ref_pattern = r'(?:Equation|Figure|Table) (\d+)\.(\d+)'
        found_refs = re.findall(ref_pattern, content)
        
        for chapter, number in found_refs:
            for ref_type in ['equation', 'figure', 'table']:
                ref_id = f"{ref_type}_{chapter}_{number}"
                if ref_id not in self.references:
                    # Check if this reference actually exists in content
                    type_pattern = f"{ref_type.title()} {chapter}\\.{number}"
                    if re.search(type_pattern, content, re.IGNORECASE):
                        continue  # Reference exists in content
                    issues.append(f"Undefined reference: {ref_type.title()} {chapter}.{number}")
        
        # Check for orphaned references (defined but never cited)
        for ref_id, ref in self.references.items():
            ref_pattern = f"{ref.type.title()} {ref.chapter}\\.{ref.number}"
            if not re.search(ref_pattern, content, re.IGNORECASE):
                issues.append(f"Orphaned reference: {ref.type.title()} {ref.chapter}.{ref.number}")
        
        return issues
    
    def insert_reference_markers(self) -> str:
        """Insert reference markers in the document for easy reference management"""
        if not os.path.exists(self.source_file):
            return "Source file not found"
        
        with open(self.source_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add reference markers for equations
        equation_pattern = r'(\$\$[^$]+\$\$)'
        equations = re.findall(equation_pattern, content)
        
        chapter_eq_count = {}
        
        def replace_equation(match):
            eq_content = match.group(1)
            # Try to determine chapter from context (simplified approach)
            chapter = 1  # Default chapter
            
            if chapter not in chapter_eq_count:
                chapter_eq_count[chapter] = 0
            chapter_eq_count[chapter] += 1
            
            return f"{eq_content} ({chapter}.{chapter_eq_count[chapter]})"
        
        # This is a simplified implementation - in practice, you'd need more sophisticated
        # chapter detection based on document structure
        
        return "Reference markers would be inserted (implementation depends on document structure)"

def main():
    """Command-line interface for reference management"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Reference and Citation Management System')
    parser.add_argument('--scan', action='store_true', help='Scan document for references')
    parser.add_argument('--validate', action='store_true', help='Validate all references')
    parser.add_argument('--list-refs', type=int, nargs='?', const=0, help='List references (optionally for specific chapter)')
    parser.add_argument('--list-cites', action='store_true', help='List all citations')
    parser.add_argument('--add-ref', nargs=4, metavar=('TYPE', 'CHAPTER', 'TITLE', 'CONTENT'), 
                       help='Add reference: type chapter title content')
    parser.add_argument('--add-cite', nargs=4, metavar=('AUTHORS', 'TITLE', 'SOURCE', 'YEAR'),
                       help='Add citation: authors title source year')
    
    args = parser.parse_args()
    
    manager = ReferenceManager()
    
    if args.scan:
        manager.scan_document_references()
        print("Document scanned for references")
    
    if args.validate:
        issues = manager.validate_references()
        if issues:
            print("Reference validation issues:")
            for issue in issues:
                print(f"  - {issue}")
        else:
            print("All references are valid")
    
    if args.list_refs is not None:
        chapter = args.list_refs if args.list_refs > 0 else None
        ref_list = manager.generate_reference_list(chapter)
        print(ref_list)
    
    if args.list_cites:
        cite_list = manager.generate_citation_list()
        print(cite_list)
    
    if args.add_ref:
        ref_type, chapter, title, content = args.add_ref
        ref_id = manager.add_reference(ref_type, int(chapter), title, content)
        print(f"Added reference: {ref_id}")
    
    if args.add_cite:
        authors, title, source, year = args.add_cite
        cite_id = manager.add_citation(authors, title, source, int(year))
        print(f"Added citation: {cite_id}")

if __name__ == "__main__":
    main()