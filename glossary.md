# Mathematical Glossary for Neural Networks

## A

**Activation Function**: A mathematical function applied to the output of each neuron in a neural network to introduce non-linearity. Common examples include sigmoid, ReLU, and tanh functions. *See: [Section 4.2 - Forward Propagation Mathematics](#42-forward-propagation-mathematics)*

**AUC (Area Under Curve)**: In pharmacokinetics, represents total drug exposure over time. Mathematically calculated as the integral of concentration over time. *See: [Section 1.1.3 - Exponential Functions](#113-exponential-functions-in-pharmacokinetics)*

## B

**Backpropagation**: The algorithm used to train neural networks by calculating gradients of the loss function with respect to network weights using the chain rule. *See: [Chapter 5 - Backpropagation Algorithm](#chapter-5-backpropagation-algorithm)*

**Bioavailability**: The fraction of an administered drug dose that reaches systemic circulation. Mathematically expressed as F = AUC_oral / AUC_iv. *See: [Section 1.4.1 - Bioavailability and Bioequivalence](#141-bioavailability-and-bioequivalence)*

## C

**Chain Rule**: A fundamental calculus rule for computing derivatives of composite functions, essential for backpropagation. If f(g(x)), then df/dx = (df/dg) × (dg/dx). *See: [Section 5.1 - Matrix Calculus](#51-matrix-calculus-comprehensive-treatment)*

**Clearance (CL)**: The volume of plasma from which a drug is completely removed per unit time. CL = Dose / AUC. *See: [Section 1.3.3 - Model Parameters](#133-model-parameters-and-clinical-interpretation)*

**Cosine Similarity**: A measure of similarity between two vectors calculated as their dot product divided by the product of their magnitudes. *See: [Section 1.2.3 - Angles and Correlation](#123-angles-and-correlation)*

**Cost Function**: A mathematical function that measures the difference between predicted and actual outputs in a neural network, used to guide learning. *See: [Section 4.3 - Optimization and Learning Theory](#43-optimization-and-learning-theory)*

## D

**Derivative**: The rate of change of a function with respect to its input variable. Fundamental to optimization in neural networks. *See: [Section 3.3 - Calculus Foundations](#33-calculus-foundations-with-neural-network-focus)*

**Dot Product**: An operation that takes two vectors and returns a scalar, calculated as the sum of products of corresponding elements. *See: [Section 2.1.5 - Dot Product](#215-dot-product-weighted-scoring-systems)*

## E

**Eigenvalue**: A scalar λ such that Av = λv for some non-zero vector v (eigenvector) and matrix A. Important in understanding system stability. *See: [Section 2.4 - Eigenvalues and Eigenvectors](#24-eigenvalues-and-eigenvectors)*

**Exponential Function**: A function of the form f(x) = ae^(bx), crucial for modeling drug elimination kinetics. *See: [Section 1.1.3 - Exponential Functions](#113-exponential-functions-in-pharmacokinetics)*

## F

**Forward Propagation**: The process of computing outputs in a neural network by passing inputs through successive layers. *See: [Section 4.2 - Forward Propagation Mathematics](#42-forward-propagation-mathematics)*

**Function**: A mathematical relationship that maps inputs to outputs. In pharmacology, used to model dose-response relationships. *See: [Section 1.1.1 - Functions in Clinical Context](#111-functions-in-clinical-context)*

## G

**Gradient**: A vector of partial derivatives indicating the direction of steepest increase of a function. Essential for neural network optimization. *See: [Section 5.2 - Backpropagation Algorithm](#52-backpropagation-algorithm-derivation)*

**Gradient Descent**: An optimization algorithm that iteratively adjusts parameters in the direction opposite to the gradient to minimize a cost function. *See: [Section 4.3 - Optimization and Learning Theory](#43-optimization-and-learning-theory)*

## H

**Half-life (t₁/₂)**: The time required for drug concentration to decrease by 50%. Related to elimination rate constant by t₁/₂ = 0.693/k. *See: [Section 1.1.3 - Exponential Functions](#113-exponential-functions-in-pharmacokinetics)*

## I

**Identity Matrix**: A square matrix with 1s on the diagonal and 0s elsewhere, serving as the multiplicative identity for matrices. *See: [Section 2.2.7 - Special Matrices](#227-special-matrices)*

## J

**Jacobian Matrix**: A matrix of first-order partial derivatives, used in backpropagation to compute gradients efficiently. *See: [Section 5.1 - Matrix Calculus](#51-matrix-calculus-comprehensive-treatment)*

## L

**Learning Rate**: A hyperparameter that controls the step size in gradient descent optimization. Too large causes instability, too small causes slow convergence. *See: [Section 4.3 - Optimization and Learning Theory](#43-optimization-and-learning-theory)*

**Linear Function**: A function of the form f(x) = mx + b, representing proportional relationships. Common in dose adjustments. *See: [Section 1.1.2 - Linear Functions](#112-linear-functions-and-drug-dosing)*

**Loss Function**: See Cost Function. *See: [Section 4.3 - Optimization and Learning Theory](#43-optimization-and-learning-theory)*

## M

**Matrix**: A rectangular array of numbers organized in rows and columns, fundamental to neural network computations. *See: [Section 2.2.1 - Matrices as Clinical Data](#221-matrices-as-clinical-data-organization)*

**Matrix Multiplication**: The operation of multiplying two matrices, central to neural network forward propagation. *See: [Section 2.2.5 - Matrix Multiplication](#225-matrix-multiplication-the-heart-of-neural-networks)*

## N

**Neural Network**: A computational model inspired by biological neural networks, consisting of interconnected nodes (neurons) organized in layers. *See: [Chapter 4 - Neural Network Fundamentals](#chapter-4-neural-network-fundamentals)*

**Neuron**: The basic computational unit in a neural network that receives inputs, applies weights and activation functions, and produces outputs. *See: [Section 4.1 - Neural Network Introduction](#41-neural-network-introduction-and-conceptual-framework)*

## O

**Optimization**: The process of finding parameter values that minimize (or maximize) an objective function, central to neural network training. *See: [Section 4.3 - Optimization and Learning Theory](#43-optimization-and-learning-theory)*

## P

**Partial Derivative**: The derivative of a multivariable function with respect to one variable while holding others constant. Essential for gradient computation. *See: [Section 3.3 - Calculus Foundations](#33-calculus-foundations-with-neural-network-focus)*

**Pharmacokinetics (PK)**: The study of drug absorption, distribution, metabolism, and elimination, often modeled mathematically. *See: [Section 1.3.2 - Types of Models](#132-types-of-models)*

## Q

**Quadratic Function**: A function of the form f(x) = ax² + bx + c, used in some dose-response modeling. *See: [Section 1.1.5 - Quadratic Functions](#115-quadratic-functions-and-dose-response)*

## R

**ReLU (Rectified Linear Unit)**: An activation function defined as f(x) = max(0, x), commonly used in neural networks. *See: [Section 4.2 - Forward Propagation Mathematics](#42-forward-propagation-mathematics)*

## S

**Scalar**: A single number, as opposed to a vector or matrix. Used in scalar multiplication operations. *See: [Section 2.1.4 - Scalar Multiplication](#214-scalar-multiplication-dose-scaling)*

**Sigmoid Function**: An S-shaped activation function f(x) = 1/(1 + e^(-x)) that maps inputs to values between 0 and 1. *See: [Section 4.2 - Forward Propagation Mathematics](#42-forward-propagation-mathematics)*

**Steady State**: The condition where drug input rate equals elimination rate, typically reached after 5 half-lives. *See: [Section 1.4.2 - Steady-State Calculations](#142-steady-state-calculations)*

## T

**Transpose**: An operation that flips a matrix over its diagonal, converting rows to columns and vice versa. *See: [Section 2.2.6 - Matrix Transpose](#226-matrix-transpose-switching-perspectives)*

## V

**Vector**: An ordered list of numbers representing multiple quantities simultaneously, fundamental to representing patient data. *See: [Section 2.1.1 - Vectors as Patient Data](#211-vectors-as-patient-data-representations)*

**Volume of Distribution (Vd)**: The theoretical volume into which a drug distributes, calculated as Dose/C₀. *See: [Section 1.3.3 - Model Parameters](#133-model-parameters-and-clinical-interpretation)*

## W

**Weight**: A parameter in a neural network that determines the strength of connection between neurons, adjusted during training. *See: [Section 4.1 - Neural Network Introduction](#41-neural-network-introduction-and-conceptual-framework)*

---

## Cross-Reference Index

### Mathematical Concepts by Chapter

**Chapter 1 - Mathematical Prerequisites**
- Functions and their applications
- Linear, exponential, logarithmic, and quadratic functions
- Coordinate systems and distance measures
- Mathematical modeling principles

**Chapter 2 - Linear Algebra Fundamentals**
- Vectors and vector operations
- Matrices and matrix operations
- Eigenvalues and eigenvectors
- Linear transformations

**Chapter 3 - Calculus Foundations**
- Derivatives and partial derivatives
- Chain rule applications
- Optimization principles
- Multivariable calculus

**Chapter 4 - Neural Network Fundamentals**
- Neural network architecture
- Forward propagation
- Activation functions
- Cost functions and optimization

**Chapter 5 - Advanced Mathematics**
- Matrix calculus
- Backpropagation algorithm
- Advanced optimization techniques
- Convergence analysis

### Pharmacological Applications by Topic

**Drug Dosing and Adjustment**
- Linear dose scaling: [Section 1.1.2](#112-linear-functions-and-drug-dosing)
- Renal dose adjustment: [Section 1.1.2](#112-linear-functions-and-drug-dosing)
- Population pharmacokinetics: [Section 1.4.3](#143-population-pharmacokinetics)

**Pharmacokinetic Modeling**
- First-order elimination: [Section 1.1.3](#113-exponential-functions-in-pharmacokinetics)
- Bioavailability calculations: [Section 1.4.1](#141-bioavailability-and-bioequivalence)
- Steady-state analysis: [Section 1.4.2](#142-steady-state-calculations)

**Drug Discovery Applications**
- QSAR modeling: [Section 6.1](#61-drug-discovery-case-study-section)
- Molecular property prediction: [Section 6.1](#61-drug-discovery-case-study-section)
- Virtual screening: [Section 6.1](#61-drug-discovery-case-study-section)

**Clinical Applications**
- Patient similarity analysis: [Section 2.1.6](#216-vector-magnitude-measuring-clinical-distance)
- Adverse event prediction: [Section 6.3](#63-clinical-trial-analysis-applications)
- Biomarker discovery: [Section 6.3](#63-clinical-trial-analysis-applications)

### Prerequisite Dependencies

**To understand Chapter 2 (Linear Algebra):**
- Complete Chapter 1, especially [Section 1.1](#11-algebra-and-functions) and [Section 1.2](#12-geometry-and-trigonometry)

**To understand Chapter 3 (Calculus):**
- Complete Chapters 1-2, especially [Section 1.1.3](#113-exponential-functions-in-pharmacokinetics) and [Section 2.1](#21-vectors-and-vector-operations)

**To understand Chapter 4 (Neural Networks):**
- Complete Chapters 1-3, especially [Section 2.2.5](#225-matrix-multiplication-the-heart-of-neural-networks) and [Section 3.3](#33-calculus-foundations-with-neural-network-focus)

**To understand Chapter 5 (Advanced Mathematics):**
- Complete Chapters 1-4, especially [Section 3.3](#33-calculus-foundations-with-neural-network-focus) and [Section 4.2](#42-forward-propagation-mathematics)